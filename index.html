<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="/favicon.svg" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <title>Comate Stack</title>
</head>
<body id='body' style="width: 100%; height:100%">
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id='root'>
        <div style="margin: 400px auto 0 auto; text-align: center;">
            <svg xmlns="http://www.w3.org/2000/svg" x="0" y="0" width="40" height="40" viewBox="0 0 50 50"><path fill="#317ff5" d="M43.935,25.145c0-10.318-8.364-18.683-18.683-18.683c-10.318,0-18.683,8.365-18.683,18.683h4.068c0-8.071,6.543-14.615,14.615-14.615c8.072,0,14.615,6.543,14.615,14.615H43.935z"><animateTransform attributeType="xml" attributeName="transform" type="rotate" from="0 25 25" to="360 25 25" dur="1s" repeatCount="indefinite"/></path></svg>
        </div>
    </div>
    <script type="module" src="/src/index.tsx"></script>
    <script>
        if (window.proxy) {
            window.proxy.__qiankunResolve__ = {};
        }
        const createDeffer = (hookName) => {
            const deffer = new Promise(resolve => {
                if (window.proxy) {
                    window.proxy.__qiankunResolve__[hookName] = resolve;
                }
            })
            return arg => deffer.then(fn => fn(arg));
        }

        ;(global => {
            global.qiankunName = '__APP_NAME__';
            global['__APP_NAME__'] = {
                bootstrap: createDeffer('bootstrap'),
                mount: createDeffer('mount'),
                unmount: createDeffer('unmount'),
                update: createDeffer('update'),
            };
        })(window);
    </script>
</body>
</html>
