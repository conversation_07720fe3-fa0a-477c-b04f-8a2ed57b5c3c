import type { SVGProps } from "react";
const SvgDirectDiff = (props: SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={243} height={99} {...props}>
        <g fill="none" fillRule="evenodd">
            <path
                fill="#81B536"
                d="M82 1.995A2 2 0 0 1 84.002 0h38.996C124.104 0 125 .893 125 1.995v14.01A1.993 1.993 0 0 1 123.01 18h-15.967l-3.543 2.769L100.348 18h-16.35A1.996 1.996 0 0 1 82 16.005z"
            />
            <path
                fill="#FFF"
                d="M92.797 5.078v-.844h1.734V3.156h1.078v1.078h4.782V3.156h1.078v1.078h1.734v.844h-1.734v3.797h2.109v.844h-3.187q1.218 1.171 3.281 1.828a6 6 0 0 1-.328.656l-.141.281q-1.312-.562-2.203-1.125v.61h-2.484V13h4.312v.844h-9.656V13h4.312v-1.031h-2.53v-.61q-1.08.75-2.157 1.266l-.14-.281q-.047-.141-.329-.656 1.875-.705 3.188-1.97h-3.094v-.843h2.11V5.078zm5.719 5.156v.985h2.296a7.2 7.2 0 0 1-1.593-1.5h-2.485a7.8 7.8 0 0 1-1.546 1.5h2.296v-.985zm-2.907-2.156v.797h4.782v-.797zm0-3v.797h4.782v-.797zm0 1.5v.797h4.782v-.797zm12.469 7.781V7.422q-.468.609-.984 1.266a7 7 0 0 0-.375-.938q-.094-.094-.094-.14a14 14 0 0 0 2.578-4.5l1.125.28q-.375.844-.89 1.829h2.671q-.328-.798-.843-1.735l1.031-.234q.515.844.891 1.781l-.75.188h3.093v.89h-2.765v1.594h2.625v.89h-2.625v1.595h2.625v.937h-2.625v1.594h2.859v.844h-6.469v.796zm-2.531-.515-1.125-.422q.703-1.875 1.406-4.594l.422.14q.422.142.703.188-.938 2.86-1.406 4.688m3.609-2.719v1.594h2.532v-1.594zm0-2.531v1.594h2.532V8.594zm0-2.485v1.594h2.532V6.11zm-3.281 1.032a45 45 0 0 0-1.266-3l.938-.328q.75 1.358 1.359 2.906z"
            />
            <path
                fill="#CBCBCB"
                fillRule="nonzero"
                d="M109 26h55v4h-55zM6.5 22a6.5 6.5 0 1 1 0 13 6.5 6.5 0 0 1 0-13m0 3a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7"
            />
            <path
                fill="#CBCBCB"
                fillRule="nonzero"
                d="M103.5 22a6.5 6.5 0 1 1 0 13 6.5 6.5 0 0 1 0-13m0 3a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7"
            />
            <path
                fill="#81B536"
                fillRule="nonzero"
                d="M168.5 22a6.5 6.5 0 1 1 0 13 6.5 6.5 0 0 1 0-13m0 3a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7"
            />
            <path
                fill="#CBCBCB"
                fillRule="nonzero"
                d="M105.5 34c0 9.16 2.911 23.097 11.486 34.752C125.491 80.312 140.132 89.757 163 90l-.043 4c-24.132-.257-39.948-10.311-49.193-22.877C104.589 58.653 101.5 43.839 101.5 34zM12 26h86v4H12z"
            />
            <path
                fill="#81B536"
                fillRule="nonzero"
                d="M168.5 86a6.5 6.5 0 1 1 0 13 6.5 6.5 0 0 1 0-13m0 3a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7"
            />
            <path
                stroke="#81B536"
                strokeDasharray={8}
                strokeWidth={1.5}
                d="M235 38v13M235 66v13"
            />
            <path
                fill="#81B536"
                fillRule="nonzero"
                d="M231.44 53.72h.8V61h-.75v-.85c-.36.66-.94.99-1.72.99-.76 0-1.35-.28-1.77-.82-.39-.5-.58-1.14-.58-1.92 0-.76.19-1.38.58-1.88.42-.56.99-.83 1.73-.83s1.31.36 1.71 1.09zm-1.54 2.63c-.57 0-1 .2-1.28.6-.26.35-.38.83-.38 1.45s.12 1.11.37 1.47c.28.4.7.61 1.26.61.52 0 .92-.2 1.21-.6.25-.36.38-.83.38-1.41v-.1c0-.6-.15-1.09-.43-1.46-.29-.38-.67-.56-1.13-.56m4.239-2.47c.17 0 .31.05.43.17q.18.15.18.42c0 .17-.06.31-.18.43-.12.11-.26.17-.43.17s-.31-.06-.43-.17a.57.57 0 0 1-.17-.43c0-.18.05-.32.17-.42.12-.12.26-.17.43-.17m-.4 1.95h.8V61h-.8zm4.369-1.97h.99v.69h-.89c-.22 0-.39.05-.49.17q-.15.165-.15.54v.57h2.33v-.59c0-.43.11-.76.33-1 .22-.26.56-.38 1.01-.38h.99v.69h-.9c-.22 0-.38.05-.48.17-.11.11-.16.29-.16.54v.57h1.47v.67h-1.47V61h-.79v-4.5h-2.33V61h-.8v-4.5h-1.3v-.67h1.3v-.59c0-.44.11-.77.33-1.01.23-.25.57-.37 1.01-.37M235 35l3.464 7.5h-6.928zM235 84l3.464-7.5h-6.928z"
            />
        </g>
    </svg>
);
export default SvgDirectDiff;
