import type { SVGProps } from "react";
const SvgMergeToTop = (props: SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width={249} height={99} {...props}>
        <g fill="none" fillRule="evenodd">
            <path
                fill="#81B536"
                fillRule="nonzero"
                d="M113.64 69.21c10.39 15.154 27.382 24.06 52.03 24.546l.079-4c-23.375-.46-39.16-8.734-48.81-22.809-6.84-9.978-9.968-21.813-10.56-33.41l-3.995.203c.626 12.271 3.935 24.79 11.256 35.47M166.5 92a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0m10 0a6.5 6.5 0 1 0-13 0 6.5 6.5 0 0 0 13 0"
            />
            <path
                fill="#CCC"
                fillRule="nonzero"
                d="M235.813 26H210v4h25.813a6.5 6.5 0 1 0 0-4m2.687 2a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0"
            />
            <path
                fill="#CCC"
                d="M163.813 30h-52.626a6.503 6.503 0 0 1-12.374 0H13.187a6.5 6.5 0 1 1 0-4h85.626a6.503 6.503 0 0 1 12.374 0h52.626a6.5 6.5 0 1 1 0 4M101.5 28a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0m68.5-3.5a3.5 3.5 0 1 1 0 7 3.5 3.5 0 0 1 0-7M3.5 28a3.5 3.5 0 1 1 7 0 3.5 3.5 0 0 1-7 0M243.445 43.441v2.548h-2V43.34l-3.393 7.255-1.811-.848 5.274-11.279.906-1.937.906 1.937 5.275 11.28-1.812.847zm-2 12.548v-5h2v5zm0 10v-5h2v5zm0 10v-5h2v5zm0 10v-5h2v5zm0 5.764v-.764h2v1.764h-5.236v-2h4.236v1zm-13.236-1h5v2h-5zm-10 0h5v2h-5zm-8.556 0h3.556v2h-3.556z"
            />
            <path
                fill="#81B536"
                d="M83 1.995A2 2 0 0 1 85.002 0h38.996C125.104 0 126 .893 126 1.995v14.01c0 1.102-.89 1.995-1.99 1.995h-15.967l-3.543 2.769L101.348 18h-16.35A1.996 1.996 0 0 1 83 16.005z"
            />
            <path
                fill="#FFF"
                d="M93.797 5.078v-.844h1.734V3.156h1.078v1.078h4.782V3.156h1.078v1.078h1.734v.844h-1.734v3.797h2.11v.844h-3.188q1.219 1.171 3.28 1.828a6 6 0 0 1-.327.656l-.14.281q-1.314-.562-2.204-1.125v.61h-2.484V13h4.312v.844h-9.656V13h4.312v-1.031h-2.53v-.61q-1.08.75-2.157 1.266l-.14-.281q-.047-.141-.329-.656 1.875-.704 3.188-1.97h-3.094v-.843h2.11V5.078zm5.719 5.156v.985h2.296a7.2 7.2 0 0 1-1.593-1.5h-2.485a7.8 7.8 0 0 1-1.546 1.5h2.296v-.985zm-2.907-2.156v.797h4.782v-.797zm0-3v.797h4.782v-.797zm0 1.5v.797h4.782v-.797zm12.47 7.781V7.422q-.47.609-.985 1.266a7 7 0 0 0-.375-.938q-.094-.095-.094-.14a14 14 0 0 0 2.578-4.5l1.125.28q-.375.844-.89 1.829h2.671q-.328-.798-.843-1.735l1.03-.234q.517.844.891 1.781l-.75.188h3.094v.89h-2.765v1.594h2.625v.89h-2.625v1.595h2.625v.937h-2.625v1.594h2.859v.844h-6.469v.796zm-2.532-.515-1.125-.422q.703-1.875 1.406-4.594l.422.14q.422.142.703.188-.938 2.86-1.406 4.688m3.61-2.719v1.594h2.53v-1.594zm0-2.531v1.594h2.53V8.594zm0-2.485v1.594h2.53V6.11zm-3.282 1.032a46 46 0 0 0-1.266-3l.938-.328q.75 1.358 1.36 2.906z"
            />
        </g>
    </svg>
);
export default SvgMergeToTop;
