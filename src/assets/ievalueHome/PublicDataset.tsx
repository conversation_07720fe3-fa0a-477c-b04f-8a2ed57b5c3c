import type { SVGProps } from "react";
const SvgPublicDataset = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={77}
        height={80}
        fill="none"
        {...props}
    >
        <foreignObject width={82.047} height={85.047} x={-3.452} y={-3.523}>
            <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                    backdropFilter: "blur(2.01px)",
                    clipPath: "url(#publicDataset_svg__a)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <path
            fill="url(#publicDataset_svg__b)"
            fillOpacity={0.8}
            fillRule="evenodd"
            d="M37.571.5c20.435 0 37 7.611 37 17v41h-.253q.252.984.253 2c0 9.389-16.565 17-37 17s-37-7.611-37-17q.001-1.016.253-2H.571v-41c0-9.389 16.566-17 37-17"
            clipRule="evenodd"
            data-figma-bg-blur-radius={4.023}
        />
        <g filter="url(#publicDataset_svg__c)" shapeRendering="crispEdges">
            <ellipse
                cx={39.071}
                cy={53}
                fill="url(#publicDataset_svg__d)"
                fillOpacity={0.4}
                rx={29.5}
                ry={13.5}
            />
            <path
                stroke="url(#publicDataset_svg__e)"
                d="M68.071 53c0 3.413-3.045 6.664-8.348 9.091C54.463 64.498 47.163 66 39.07 66c-8.091 0-15.392-1.502-20.651-3.909-5.304-2.427-8.349-5.678-8.349-9.091s3.045-6.664 8.349-9.091C23.679 41.502 30.98 40 39.07 40s15.392 1.502 20.652 3.909c5.303 2.427 8.348 5.678 8.348 9.091Z"
            />
        </g>
        <g filter="url(#publicDataset_svg__f)" shapeRendering="crispEdges">
            <ellipse
                cx={39.071}
                cy={38}
                fill="url(#publicDataset_svg__g)"
                fillOpacity={0.8}
                rx={29.5}
                ry={13.5}
            />
            <path
                stroke="url(#publicDataset_svg__h)"
                d="M68.071 38c0 3.413-3.045 6.664-8.348 9.091C54.463 49.498 47.163 51 39.07 51c-8.091 0-15.392-1.502-20.651-3.909-5.304-2.427-8.349-5.678-8.349-9.091s3.045-6.664 8.349-9.091C23.679 26.502 30.98 25 39.07 25s15.392 1.502 20.652 3.909c5.303 2.427 8.348 5.678 8.348 9.091Z"
            />
        </g>
        <g filter="url(#publicDataset_svg__i)">
            <ellipse
                cx={38.071}
                cy={21}
                fill="url(#publicDataset_svg__j)"
                rx={29.5}
                ry={13.5}
            />
            <path
                stroke="url(#publicDataset_svg__k)"
                d="M67.071 21c0 3.413-3.045 6.664-8.348 9.091C53.463 32.498 46.163 34 38.07 34c-8.091 0-15.392-1.502-20.651-3.909C12.115 27.664 9.07 24.413 9.07 21s3.045-6.664 8.349-9.091C22.679 9.502 29.98 8 38.07 8s15.392 1.502 20.652 3.909c5.303 2.427 8.348 5.678 8.348 9.091Z"
            />
        </g>
        <foreignObject width={40.4} height={59} x={36.571} y={20.1}>
            <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                    backdropFilter: "blur(1.2px)",
                    clipPath: "url(#publicDataset_svg__l)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <g data-figma-bg-blur-radius={2.4} filter="url(#publicDataset_svg__m)">
            <mask id="publicDataset_svg__o" fill="#fff">
                <path
                    fillRule="evenodd"
                    d="M46.571 41.14V75.5h28v-47h-.016c-3.285 6.344-14.209 11.3-27.984 12.64"
                    clipRule="evenodd"
                />
            </mask>
            <path
                fill="#B5CAFF"
                fillOpacity={0.4}
                fillRule="evenodd"
                d="M46.571 41.14V75.5h28v-47h-.016c-3.285 6.344-14.209 11.3-27.984 12.64"
                clipRule="evenodd"
                shapeRendering="crispEdges"
            />
            <path
                fill="url(#publicDataset_svg__n)"
                d="M46.571 75.5h-.5v.5h.5zm0-34.36-.048-.497-.452.044v.454zm28 34.36v.5h.5v-.5zm0-47h.5V28h-.5zm-.016 0V28h-.304l-.14.27zm-27.484 47V41.14h-1V75.5zm27.5-.5h-28v1h28zm-.5-46.5v47h1v-47zm.484.5h.016v-1h-.016zm-.444-.73c-1.568 3.029-5.003 5.793-9.82 7.971-4.804 2.173-10.924 3.736-17.768 4.402l.096.995c6.931-.674 13.161-2.259 18.084-4.486 4.91-2.22 8.58-5.107 10.296-8.422z"
                mask="url(#publicDataset_svg__o)"
            />
        </g>
        <g filter="url(#publicDataset_svg__p)">
            <path fill="#fff" d="m62.071 44.625 5.22 9.041h-10.44z" />
        </g>
        <g filter="url(#publicDataset_svg__q)">
            <path fill="#fff" d="m62.071 55.584 5.22 9.041h-10.44z" />
        </g>
        <defs>
            <linearGradient
                id="publicDataset_svg__d"
                x1={54.071}
                x2={23.447}
                y1={46}
                y2={73.47}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#7CF7FF" />
                <stop offset={0.275} stopColor="#67CCFF" />
                <stop offset={0.65} stopColor="#40A8FF" />
                <stop offset={1} stopColor="#6981FF" />
            </linearGradient>
            <linearGradient
                id="publicDataset_svg__e"
                x1={59.571}
                x2={44.208}
                y1={69.5}
                y2={42.525}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.8} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="publicDataset_svg__g"
                x1={54.071}
                x2={28.622}
                y1={31}
                y2={54.856}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#7CF7FF" />
                <stop offset={0.275} stopColor="#67CCFF" />
                <stop offset={0.65} stopColor="#40A8FF" />
                <stop offset={1} stopColor="#6981FF" />
            </linearGradient>
            <linearGradient
                id="publicDataset_svg__h"
                x1={64.071}
                x2={40.534}
                y1={43.5}
                y2={22.483}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.8} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="publicDataset_svg__j"
                x1={59.071}
                x2={23.265}
                y1={15}
                y2={44.088}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#7CF7FF" />
                <stop offset={0.275} stopColor="#67CCFF" />
                <stop offset={0.551} stopColor="#40A8FF" />
                <stop offset={1} stopColor="#6981FF" />
            </linearGradient>
            <linearGradient
                id="publicDataset_svg__k"
                x1={63.571}
                x2={40.565}
                y1={28.5}
                y2={5.811}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.8} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="publicDataset_svg__n"
                x1={48.815}
                x2={63.136}
                y1={25.691}
                y2={29.653}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.4} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <filter
                id="publicDataset_svg__c"
                width={65.628}
                height={33.628}
                x={4.6}
                y={39.5}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1.657} dy={3.314} />
                <feGaussianBlur stdDeviation={1.657} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_354_1675"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_354_1675"
                    result="shape"
                />
            </filter>
            <filter
                id="publicDataset_svg__f"
                width={65.628}
                height={33.628}
                x={4.6}
                y={24.5}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1.657} dy={3.314} />
                <feGaussianBlur stdDeviation={1.657} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_354_1675"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_354_1675"
                    result="shape"
                />
            </filter>
            <filter
                id="publicDataset_svg__i"
                width={65.628}
                height={33.628}
                x={3.6}
                y={7.5}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1.657} dy={3.314} />
                <feGaussianBlur stdDeviation={1.657} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_354_1675"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_354_1675"
                    result="shape"
                />
            </filter>
            <filter
                id="publicDataset_svg__m"
                width={40.4}
                height={59}
                x={36.571}
                y={20.1}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-4} dy={-2.4} />
                <feGaussianBlur stdDeviation={3} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_354_1675"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_354_1675"
                    result="shape"
                />
            </filter>
            <filter
                id="publicDataset_svg__p"
                width={19.207}
                height={17.808}
                x={52.468}
                y={40.241}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={2.192} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.0779394 0 0 0 0 0.389697 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_354_1675"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_354_1675"
                    result="shape"
                />
            </filter>
            <filter
                id="publicDataset_svg__q"
                width={19.207}
                height={17.808}
                x={52.468}
                y={51.2}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={2.192} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0.0779394 0 0 0 0 0.389697 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_354_1675"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_354_1675"
                    result="shape"
                />
            </filter>
            <clipPath
                id="publicDataset_svg__a"
                transform="translate(3.452 3.523)"
            >
                <path
                    fillRule="evenodd"
                    d="M37.571.5c20.435 0 37 7.611 37 17v41h-.253q.252.984.253 2c0 9.389-16.565 17-37 17s-37-7.611-37-17q.001-1.016.253-2H.571v-41c0-9.389 16.566-17 37-17"
                    clipRule="evenodd"
                />
            </clipPath>
            <clipPath
                id="publicDataset_svg__l"
                transform="translate(-36.571 -20.1)"
            >
                <path
                    fillRule="evenodd"
                    d="M46.571 41.14V75.5h28v-47h-.016c-3.285 6.344-14.209 11.3-27.984 12.64"
                    clipRule="evenodd"
                />
            </clipPath>
            <radialGradient
                id="publicDataset_svg__b"
                cx={0}
                cy={0}
                r={1}
                gradientTransform="matrix(0 51.7081 -96.6054 0 37.571 32.177)"
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.5} />
                <stop offset={1} stopColor="#7BA1FF" stopOpacity={0.6} />
            </radialGradient>
        </defs>
    </svg>
);
export default SvgPublicDataset;
