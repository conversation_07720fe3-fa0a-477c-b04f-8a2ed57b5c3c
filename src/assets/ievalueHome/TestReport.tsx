import type { SVGProps } from "react";
const SvgTestReport = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={96}
        height={95}
        fill="none"
        {...props}
    >
        <foreignObject width={81.244} height={97.946} x={7.823} y={-4.023}>
            <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                    backdropFilter: "blur(2.01px)",
                    clipPath: "url(#testReport_svg__a)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <path
            fill="url(#testReport_svg__b)"
            fillRule="evenodd"
            d="M27.571 3.5a3.5 3.5 0 1 1 7 0v2.916h28V3.5a3.5 3.5 0 0 1 7 0v2.916h6.42c5 0 9.053 4.053 9.053 9.053v65.378c0 5-4.053 9.053-9.052 9.053H43.027c-17.22 0-31.18-13.96-31.18-31.18V15.468c0-5 4.053-9.053 9.052-9.053h6.672z"
            clipRule="evenodd"
            data-figma-bg-blur-radius={4.023}
        />
        <g filter="url(#testReport_svg__c)">
            <rect
                width={61.695}
                height={43.25}
                x={19.166}
                y={38.603}
                fill="url(#testReport_svg__d)"
                rx={4.971}
            />
            <rect
                width={60.661}
                height={42.217}
                x={19.683}
                y={39.12}
                stroke="url(#testReport_svg__e)"
                strokeWidth={1.034}
                rx={4.454}
            />
        </g>
        <g filter="url(#testReport_svg__f)">
            <rect
                width={43}
                height={19}
                x={37.571}
                y={15}
                fill="url(#testReport_svg__g)"
                rx={4.971}
            />
            <rect
                width={41.966}
                height={17.966}
                x={38.088}
                y={15.517}
                stroke="url(#testReport_svg__h)"
                strokeWidth={1.034}
                rx={4.454}
            />
        </g>
        <foreignObject width={52.713} height={41.742} x={2.813} y={52.181}>
            <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                    backdropFilter: "blur(2.01px)",
                    clipPath: "url(#testReport_svg__i)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <g data-figma-bg-blur-radius={4.023} filter="url(#testReport_svg__j)">
            <path
                fill="#C2D3FF"
                fillOpacity={0.53}
                d="M12.892 64.251a22.71 22.71 0 0 0 25.524 6.478l1.141-.443a24.12 24.12 0 0 0 8.386 18.281l1.548 1.333H37.517c-14.005 0-25.195-11.656-24.625-25.649"
                shapeRendering="crispEdges"
            />
        </g>
        <g fill="#fff" opacity={0.5}>
            <rect
                width={13.594}
                height={4.023}
                x={26.486}
                y={50.673}
                rx={1.006}
            />
            <rect
                width={13.594}
                height={4.023}
                x={26.486}
                y={50.673}
                rx={1.006}
            />
            <rect
                width={13.594}
                height={4.023}
                x={26.486}
                y={50.673}
                rx={1.006}
            />
            <rect
                width={13.594}
                height={4.023}
                x={26.486}
                y={50.673}
                rx={1.006}
            />
        </g>
        <g fill="#fff" opacity={0.5}>
            <rect
                width={24.051}
                height={4.023}
                x={26.486}
                y={59.725}
                rx={1.006}
            />
            <rect
                width={24.051}
                height={4.023}
                x={26.486}
                y={59.725}
                rx={1.006}
            />
            <rect
                width={24.051}
                height={4.023}
                x={26.486}
                y={59.725}
                rx={1.006}
            />
            <rect
                width={24.051}
                height={4.023}
                x={26.486}
                y={59.725}
                rx={1.006}
            />
        </g>
        <path
            fill="#EEFCFF"
            d="M64.76 21.777h2.83l.99 1.735 1.15-1.735h2.63l-2.121 2.965L72.512 28H69.73l-1.148-2.004L67.227 28h-2.584l2.262-3.258zM55.757 21.777h2.83l.99 1.735 1.148-1.735h2.631l-2.12 2.965L63.508 28h-2.784l-1.148-2.004L58.223 28H55.64l2.262-3.258zM46.753 21.777h2.83l.99 1.735 1.148-1.735h2.631l-2.12 2.965L54.504 28H51.72l-1.148-2.004L49.219 28h-2.584l2.262-3.258z"
        />
        <defs>
            <linearGradient
                id="testReport_svg__d"
                x1={59.204}
                x2={30.215}
                y1={31.485}
                y2={96.662}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#7CF7FF" />
                <stop offset={0.275} stopColor="#67CCFF" />
                <stop offset={0.565} stopColor="#40A8FF" />
                <stop offset={1} stopColor="#6981FF" />
            </linearGradient>
            <linearGradient
                id="testReport_svg__e"
                x1={36.071}
                x2={30.613}
                y1={36}
                y2={48.204}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.6} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="testReport_svg__g"
                x1={65.476}
                x2={56.562}
                y1={11.873}
                y2={43.671}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#7CF7FF" />
                <stop offset={0.275} stopColor="#67CCFF" />
                <stop offset={0.565} stopColor="#40A8FF" />
                <stop offset={1} stopColor="#6981FF" />
            </linearGradient>
            <linearGradient
                id="testReport_svg__h"
                x1={44.071}
                x2={40.711}
                y1={8}
                y2={17.383}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.6} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <filter
                id="testReport_svg__c"
                width={68.323}
                height={49.879}
                x={14.195}
                y={38.603}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1.657} dy={3.314} />
                <feGaussianBlur stdDeviation={1.657} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_352_1603"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_352_1603"
                    result="shape"
                />
            </filter>
            <filter
                id="testReport_svg__f"
                width={49.628}
                height={25.628}
                x={32.6}
                y={15}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1.657} dy={3.314} />
                <feGaussianBlur stdDeviation={1.657} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_352_1603"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_352_1603"
                    result="shape"
                />
            </filter>
            <filter
                id="testReport_svg__j"
                width={52.713}
                height={41.742}
                x={2.813}
                y={52.181}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-2.012} dy={-4.023} />
                <feGaussianBlur stdDeviation={4.023} />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0" />
                <feBlend
                    in2="BackgroundImageFix"
                    result="effect1_dropShadow_352_1603"
                />
                <feBlend
                    in="SourceGraphic"
                    in2="effect1_dropShadow_352_1603"
                    result="shape"
                />
            </filter>
            <clipPath
                id="testReport_svg__a"
                transform="translate(-7.823 4.023)"
            >
                <path
                    fillRule="evenodd"
                    d="M27.571 3.5a3.5 3.5 0 1 1 7 0v2.916h28V3.5a3.5 3.5 0 0 1 7 0v2.916h6.42c5 0 9.053 4.053 9.053 9.053v65.378c0 5-4.053 9.053-9.052 9.053H43.027c-17.22 0-31.18-13.96-31.18-31.18V15.468c0-5 4.053-9.053 9.052-9.053h6.672z"
                    clipRule="evenodd"
                />
            </clipPath>
            <clipPath
                id="testReport_svg__i"
                transform="translate(-2.813 -52.181)"
            >
                <path d="M12.892 64.251a22.71 22.71 0 0 0 25.524 6.478l1.141-.443a24.12 24.12 0 0 0 8.386 18.281l1.548 1.333H37.517c-14.005 0-25.195-11.656-24.625-25.649" />
            </clipPath>
            <radialGradient
                id="testReport_svg__b"
                cx={0}
                cy={0}
                r={1}
                gradientTransform="matrix(.12555 54.95226 -85.67518 .19574 48.446 38.548)"
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#fff" stopOpacity={0.5} />
                <stop offset={1} stopColor="#7BA1FF" stopOpacity={0.65} />
            </radialGradient>
        </defs>
    </svg>
);
export default SvgTestReport;
