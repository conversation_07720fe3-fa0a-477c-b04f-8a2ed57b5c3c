/* eslint-disable max-lines */
import {Checkbox, Flex, Form, TableColumnsType, Tabs, Typography} from 'antd';
import {Button} from '@panda-design/components';
import {useCallback, useEffect, useMemo, useState} from 'react';
import styled from '@emotion/styled';
import {keys} from 'lodash';
import {IconDownSolid} from '@baidu/ee-icon';
import {Path} from '@panda-design/path-form';
import {IconSubtract} from '@/icons/mcp';
import {Gap} from '@/design/iplayground/Gap';
import {IconPlus} from '@/icons/lucide';
import {BaseParam} from '@/types/mcp/mcp';
import {StyledTable} from '../ParamList';
import {useActiveTool} from '../hooks';
import {getParamValueFieldPath, useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';
import ParamValueField from './ParamValueField';

const StyledTabs = styled(Tabs)`
    .ant-5-tabs-tab-active{
        .ant-5-tabs-tab-btn{
            color: #0083FF !important;
        }
    }
    .ant-5-tabs-tab{
        font-size: 14px;
        &:not(:first-child) {
            margin-left: 24px;
        }
    }
`;
const ItemWrapper = styled.div<{activeKey: string, deep?: number}>`
    position: relative;
    padding-left: ${props => (
        props.activeKey === 'body'
            ? 16 + (props.deep + 1) * 12 + 'px'
            : '42px'
    )};
    .button-wrapper {
        position: absolute;
        top: -4px;
        left: -8px;
    }
`;

const StyledTriangle = styled(IconDownSolid) <{expanded?: boolean}>`
    color: #BFBFBF;
    position: absolute;
    left: 32px;
    top: ${props => (props.expanded ? '21px' : '19px')};
    z-index: 10;
    margin-right: 8px;
    cursor: pointer;
    transform: ${props => (props.expanded ? 'rotate(0deg)' : 'rotate(-90deg)')};
`;

/**
 * 参数path是格式化数据源的时候生成的，在transformBodySchemaToTableTreeData函数中。
 */
export const transformPathInBody = (path: Path): Path => {
    return path.reduce(
        (acc, cur, index) => {
            return [
                ...acc,
                ...index > 0 ? ['children', cur] : [cur],
            ];
        }, []
    );
};


export const isParamRelated = (key: string, paramsList: BaseParam[]) => {
    return paramsList?.some(
        item => item.refParam === key
    );
};

interface NameFieldProps{
    name: string;
    record: BaseParam;
    index: number;
    activeKey: string;
    sourcePath: Array<string|number>;
}

const ParamNameField = ({name, record, activeKey, sourcePath}: NameFieldProps) => {
    const {toolApiConfigDataRef, addToolParam, removeToolParam} = useToolParamsConfigContext();
    const {activeToolIndex} = useActiveTool();
    const paramListPath = useMemo(
        () => ['tools', activeToolIndex, 'toolParams', 'toolParams'],
        [activeToolIndex]
    );
    const paramsList: BaseParam[] = Form.useWatch(paramListPath);
    const toolApiConfigDataRefCurrent = toolApiConfigDataRef.current;
    const valueFieldPath = useMemo(
        () => {
            return getParamValueFieldPath({
                basePath: [...sourcePath, activeKey],
                record,
                allParams: toolApiConfigDataRefCurrent,
            });
        },
        [activeKey, record, sourcePath, toolApiConfigDataRefCurrent]
    );
    const handleRelate = useCallback(
        (record: BaseParam) => {
            addToolParam(activeToolIndex, record);
        },
        [addToolParam, activeToolIndex]
    );
    const handleUnRelate = useCallback(
        (record: BaseParam) => {
            removeToolParam(activeToolIndex, record.key);
        },
        [removeToolParam, activeToolIndex]
    );

    const value = Form.useWatch(valueFieldPath);
    const canRelate = useCallback(
        (record: BaseParam) => {
            // 有值不能关联，用户的任何输入，都默认是该参数的常量值
            return record.canRelate && !value;
        },
        [value]
    );
    return (
        <ItemWrapper activeKey={activeKey} deep={record?.fieldPath?.length}>
            {
                isParamRelated(record.key, paramsList)
                    ? (
                        <span className="button-wrapper">
                            <Button
                                onClick={() => handleUnRelate(record)}
                                type="text"
                                tooltip="取消关联"
                                icon={<IconSubtract />}
                            />
                        </span>
                    )
                // object不能给tool添加参数
                    : canRelate(record) && (
                        <span className="button-wrapper">
                            <Button
                                onClick={() => handleRelate(record)}
                                type="text"
                                tooltip="关联"
                                icon={<IconPlus />}
                            />
                        </span>
                    )
            }
            {name}
        </ItemWrapper>
    );
};

// ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
const ParamsConfig = () => {
    const [showRequired, setShowRequired] = useState(false);
    // path、query、cookie、body等，source对象下的key
    const [activeKey, setActiveKey] = useState<string>();
    const {activeToolIndex} = useActiveTool();
    const sourcePath = useMemo(
        () => ['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters'],
        [activeToolIndex]
    );
    const source = Form.useWatch(sourcePath);
    const tabItems = useMemo(
        () => {
            const sourceKeys = keys(source);
            const items = sourceKeys.map(key => ({
                label: key,
                value: key,
                key,
            }));
            return items;
        },
        [source]
    );
    useEffect(
        () => {
            if (!activeKey) {
                setActiveKey(tabItems?.[0]?.key);
            }
        },
        [activeKey, tabItems]
    );

    const columns = useMemo<TableColumnsType<BaseParam>>(
        () => [
            {
                title: '参数名',
                dataIndex: 'name',
                width: 200,
                render: (name: string, record, index) => (
                    <ParamNameField
                        name={name}
                        record={record}
                        index={index}
                        activeKey={activeKey}
                        sourcePath={sourcePath}
                    />
                ),
            },
            {
                title: '类型',
                width: 60,
                dataIndex: 'type',
            },
            {
                title: '是否必须',
                width: 60,
                dataIndex: 'required',
                render: (required: boolean) => (required ? '是' : '否'),
            },
            {
                title: '说明',
                width: 150,
                dataIndex: 'description',
                render: description => (
                    <Typography.Paragraph ellipsis={{rows: 2, tooltip: {title: description}}}>
                        {description}
                    </Typography.Paragraph>
                ),
            },
            {
                title: '默认值',
                dataIndex: 'value',
                width: 150,
                render: (_, record) => {
                    return (
                        <ParamValueField
                            basePath={[...sourcePath, activeKey]}
                            record={record}
                        />
                    );
                },
            },
        ],
        [activeKey, sourcePath]
    );
    window.console.log(activeKey, source?.[activeKey]);
    return (
        <Flex vertical>
            <Flex justify="space-between">
                <StyledTabs activeKey={activeKey} onChange={setActiveKey} items={tabItems} />
                <Checkbox
                    checked={showRequired}
                    onChange={e => setShowRequired(e.target.checked)}
                    style={{alignItems: 'center'}}
                >
                    仅看必填项
                </Checkbox>
            </Flex>
            <Gap />
            <Form.Item name={sourcePath} style={{marginBottom: 0}}>
                <StyledTable
                    rowKey="key"
                    expandable={{
                        defaultExpandAllRows: true,
                        expandIcon: ({expanded, onExpand, record}) => {
                            window.console.log('record', record);
                            if (record?.children) {
                                return (
                                    <span onClick={e => onExpand(record, e)}>
                                        <StyledTriangle expanded={expanded} />
                                    </span>
                                );
                            }
                        },
                    }}
                    dataSource={source?.[activeKey] || []}
                    columns={columns}
                    pagination={{hideOnSinglePage: true}}
                />
            </Form.Item>
        </Flex>
    );
};

export default ParamsConfig;

