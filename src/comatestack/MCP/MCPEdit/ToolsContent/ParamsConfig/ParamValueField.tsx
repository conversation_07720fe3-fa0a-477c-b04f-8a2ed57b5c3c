import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useMemo} from 'react';
import {BaseParam} from '@/types/mcp/mcp';
import {useActiveTool} from '../hooks';
import {getParamValueFieldPath, useToolParamsConfigContext} from '../Provider/toolParamsConfigProvider';
import {isParamRelated} from './index';

const ParamValueField = ({basePath, record}: {
    basePath: Path;
    record: BaseParam;
}) => {
    const {toolApiConfigDataRef} = useToolParamsConfigContext();
    const {activeToolIndex} = useActiveTool();
    const paramsList: BaseParam[] = Form.useWatch(['tools', activeToolIndex, 'toolParams', 'toolParams']);
    const disabled = isParamRelated(record.key, paramsList);
    const toolApiConfigDataRefCurrent = toolApiConfigDataRef.current;
    const path = useMemo(
        () => {
            return getParamValueFieldPath({basePath, record, allParams: toolApiConfigDataRefCurrent});
        },
        [basePath, record, toolApiConfigDataRefCurrent]
    );

    if (record.canRelate === false) {
        return null;
    }
    return (
        <Form.Item
            style={{marginBottom: 0}}
            name={path}
        >
            <Input placeholder="请输入" disabled={disabled} />
        </Form.Item>
    );
};

export default ParamValueField;

