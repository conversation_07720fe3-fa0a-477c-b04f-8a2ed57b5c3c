/* eslint-disable complexity */
/* eslint-disable max-lines */
import {Form} from 'antd';
import {keys} from 'lodash';
import {useCallback, useMemo} from 'react';
import {message} from '@panda-design/components';
import {useSearchParams} from '@panda-design/router';
import {BaseParam} from '@/types/mcp/mcp';
import {apiPostMCPTool, apiPutMCPTool} from '@/api/mcp';
import {useMCPServerId} from '@/components/MCP/hooks';
import {loadMCPServerToolItem} from '@/regions/mcp/mcpServer';
import {useSearchReplace} from '@/hooks/icode/common/useSearchParams';
import {errorRowCss} from '..';
import {resetTouchedBasePath} from '../regions';
import {fillParamValueToJSONProperties, getBodySchemaTreeData, TreeData} from '../utils';

export const useActiveTool = () => {
    const {activeToolIndex} = useSearchParams();
    const replace = useSearchReplace();
    const setActiveToolIndex = (index: number) => {
        replace({activeToolIndex: index});
    };
    return {activeToolIndex: Number(activeToolIndex || -1), setActiveToolIndex};
};

interface Props {
    canSelectRoot?: boolean;
    titleWidthRoot?: boolean;
}

export const useHandleRelateParamTreeData = ({canSelectRoot = true, titleWidthRoot}: Props): TreeData[] => {
    const {activeToolIndex} = useActiveTool();
    const paramConfigs = Form.useWatch(['tools', activeToolIndex, 'toolConf', 'openapiConf', 'parameters']);

    const handleTrees = useMemo(
        () => {
            const roots = keys(paramConfigs);
            const data = roots
                .filter(root => paramConfigs?.[root]?.length > 0)
                .map(root => ({
                    title: root,
                    value: root,
                    disabled: !canSelectRoot,
                    key: root,
                    children:
                        root === 'body'
                            ? getBodySchemaTreeData(paramConfigs?.[root], titleWidthRoot)
                            : paramConfigs?.[root].map((item: any) => ({
                                title: titleWidthRoot ? `${root}.${item?.name}` : item?.name,
                                value: item.key,
                                key: item.key,
                            })),
                }));
            return data;
        },
        [canSelectRoot, paramConfigs, titleWidthRoot]
    );
    return handleTrees;
};

interface SaveToolProps {
    on: () => void;
    off: () => void;
    onSuccess?: () => void;
}

export const useHandleSaveTool = ({on, off, onSuccess}: SaveToolProps): (() => Promise<void>) => {
    const {validateFields, setFieldValue} = Form.useFormInstance();
    const mcpServerId = useMCPServerId();
    const {activeToolIndex} = useActiveTool();

    const handleSubmit = useCallback(
        // eslint-disable-next-line max-statements
        async () => {
            try {
                on();
                const values = await validateFields();
                const {tools, serverSourceType} = values;
                const toolItem = tools?.[activeToolIndex];
                const {toolConf, id, toolParams, ...rest} = toolItem;
                const {parameters, requestBody, ...otherOpenapiConf} = toolConf?.openapiConf ?? {};
                const {body, ...otherParameters} = parameters ?? {};
                const newRequestBody = {
                    ...requestBody,
                    jsonSchema: fillParamValueToJSONProperties(body, requestBody?.jsonSchema),
                };
                const newTool = {
                    mcpServerId,
                    ...rest,
                    toolConf: {
                        ...toolConf,
                        scriptSource: serverSourceType === 'script' ? 'cmd' : undefined,
                        openapiConf: toolConf?.openapiConf
                            ? {
                                ...otherOpenapiConf,
                                parameters: {
                                    ...otherParameters,
                                },
                                requestBody: newRequestBody,
                            }
                            : undefined,
                        serverSourceType,
                    },
                    toolParams: {
                        ...toolParams,
                        toolParams: toolParams.toolParams.map((item: BaseParam) => ({
                            ...item,
                            dataType: item.dataType || item.type,
                        })),
                    },
                };
                if (id) {
                    try {
                        await apiPutMCPTool({
                            toolId: id,
                            ...newTool,
                        });
                        loadMCPServerToolItem({toolId: id, mcpServerId: mcpServerId});
                        message.success('保存成功');
                        onSuccess?.();
                        setFieldValue(['tools', activeToolIndex, 'toolStatus'], 'complete');
                        off();
                        resetTouchedBasePath();
                    } catch (e) {
                        off();
                        message.error('保存失败');
                        console.error(e);
                    }
                } else {
                    try {
                        const tool = await apiPostMCPTool({
                            mcpServerId,
                            toolStatus: 'complete',
                            ...newTool,
                        });
                        setFieldValue(['tools', activeToolIndex, 'id'], tool.id);
                        setFieldValue(['tools', activeToolIndex, 'toolStatus'], tool.toolStatus);
                        message.success('保存成功');
                        onSuccess?.();
                        off();
                        resetTouchedBasePath();
                    } catch (e) {
                        off();
                        message.error('保存失败');
                        console.error(e);
                    }
                }
            } catch (e) {
                console.error(e);
                const toolParamsRows = document.querySelectorAll('.toolParamsTableRow');
                e?.errorFields?.forEach((field: any) => {
                    if (field.name?.join('.')?.includes('toolParams.toolParams')) {
                        const toolParamIndex = field.name[field.name.length - 2];
                        const errorRow = toolParamsRows[toolParamIndex];
                        if (errorRow) {
                            errorRow.classList.add(errorRowCss);
                        }
                    }
                });
                message.warning('请检查表单信息');
                off();
            }
        },
        [activeToolIndex, mcpServerId, off, on, onSuccess, setFieldValue, validateFields]
    );
    return handleSubmit;
};
