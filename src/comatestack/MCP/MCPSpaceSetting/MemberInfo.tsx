import {Flex, Input, Table} from 'antd';
import type {TableProps} from 'antd';
import {SearchOutlined} from '@ant-design/icons';
import {useMemo, useState} from 'react';
import {AddMemberButton} from '@/components/MCP/AddMemberButton';
import {useMCPWorkspaceInfo} from '@/components/MCP/hooks';
import {MCPSpaceMember} from '@/types/mcp/mcp';
import UserAvatar from '@/design/UserAvatar';
import RoleSelect from './RoleSelect';
import DeleteUserButton from './DeleteUserButton';
import PermissionControl from './PermissionControl';

const columns: TableProps<MCPSpaceMember>['columns'] = [
    {
        title: '用户名',
        dataIndex: 'value',
        key: 'value',
        render: label => <UserAvatar username={label} showText />,
    },
    {
        title: '邮箱',
        dataIndex: 'label',
        key: 'label',
        render: (label, {value}) => <span>{value}@baidu.com</span>,
    },
    {
        title: '权限',
        dataIndex: 'role',
        key: 'role',
        render: (_, item) => <RoleSelect item={item} />,
    },
    {
        title: '操作',
        key: 'action',
        render: (_, item) => <DeleteUserButton item={item} />,
    },
];
const MemberInfo = () => {
    const mcpWorkspaceInfo = useMCPWorkspaceInfo();
    const [searchTxt, setSearchKeyword] = useState<string>('');

    const filteredMembers = useMemo(
        () => {
            if (!searchTxt) {
                return mcpWorkspaceInfo?.member;
            }
            const validSearchTxt = searchTxt.trim();
            return mcpWorkspaceInfo?.member?.filter(item =>
                // 有用户在这里报错，兼容一下数据的异常情况
                item.label?.includes(validSearchTxt) || item.value?.includes(validSearchTxt)
            );
        },
        [searchTxt, mcpWorkspaceInfo?.member]
    );

    return (
        <Flex vertical gap={8}>
            <Flex justify="space-between" gap={8}>
                <PermissionControl>
                    <AddMemberButton />
                </PermissionControl>
                <Input
                    style={{width: 240}}
                    placeholder="输入关键词检索"
                    suffix={<SearchOutlined />}
                    allowClear
                    onChange={e => setSearchKeyword(e.target.value)}
                />
            </Flex>
            <Table
                rowKey="value"
                columns={columns}
                dataSource={filteredMembers}
            />
        </Flex>
    );
};

export default MemberInfo;
