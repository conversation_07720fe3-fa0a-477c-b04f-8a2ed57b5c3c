import {Form, Radio} from 'antd';
import {Path} from '@panda-design/path-form';
import {useEffect} from 'react';
import ImportByIApiField from './ImportByIApiField';
import ImportByOpenApiField from './ImportByOpenApiField';

interface Props {
    hidden: boolean;
    path: Path;
}

const ImportField = ({hidden, path}: Props) => {
    const importType = Form.useWatch([...path, 'import', 'type']);
    const {setFieldValue} = Form.useFormInstance();
    useEffect(
        () => {
            if (!importType) {
                setFieldValue([...path, 'import', 'type'], 'openapi');
            }
        },
        [importType, path, setFieldValue]
    );
    return (
        <div style={{display: hidden ? 'none' : 'block'}}>
            <Form.Item
                name={[...path, 'import', 'type']}
                label="导入方式"
                rules={[
                    {required: true, message: '请选择导入方式'},
                ]}
            >
                <Radio.Group>
                    <Radio value="openapi">OpenAPI/Swagger</Radio>
                    {/* <Radio value="iapi">iAPI项目</Radio> */}
                </Radio.Group>
            </Form.Item>

            {
                importType === 'openapi'
                && (
                    <ImportByOpenApiField path={path} />
                )
            }
            {
                importType === 'iapi'
                && (
                    <ImportByIApiField path={[...path, 'iapi']} />
                )
            }
        </div>
    );
};

export default ImportField;
