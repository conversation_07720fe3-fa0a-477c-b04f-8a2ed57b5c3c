import {Form} from 'antd';
import {Path} from '@panda-design/path-form';
import OpenAPIImportResult from './OpenApiImportResult';
import ImportByOpenApiWidthFileUpload from './ImportByOpenApiWidthFileUpload';

/* const DivideLine = styled.div`
const DivideLine = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
    &:before{
        content: '';
        width: 1px;
        height: 100%;
        background-color: #e4e4e4;
        flex-grow: 1;
    };
    &: after{
        content: '';
        width: 1px;
        height: 100%;
        background-color: #e4e4e4;
        flex-grow: 1;
    }
`; */

interface Props{
    path: Path;
}

const ImportByOpenApiField = ({path}: Props) => {
    return (
        <>
            <Form.Item
                name={[...path, 'import', 'fileList']}
                label="批量导入"
                rules={[{required: true, message: '请导入文件'}]}
            >
                <ImportByOpenApiWidthFileUpload path={path} />
            </Form.Item>
            {/* <Flex gap={16}>
                <DivideLine>
                    <span>OR</span>
                </DivideLine>
                <Flex vertical>
                    <Form.Item
                        label="URL方式导入"
                        name={['serverConf', 'url']}
                        rules={[{required: true, message: '请输入URL'}]}
                    >
                        <Input placeholder="请输入URL" />
                    </Form.Item>
                    <Form.Item
                        label="BasicAuth"
                        name={['serverConf', 'import', 'basicAuth']}
                        rules={[{required: true, message: '请输入BasicAuth'}]}
                    >
                        <Switch />
                    </Form.Item>
                </Flex>
            </Flex> */}
            <OpenAPIImportResult path={path} />
        </>
    );
};

export default ImportByOpenApiField;

