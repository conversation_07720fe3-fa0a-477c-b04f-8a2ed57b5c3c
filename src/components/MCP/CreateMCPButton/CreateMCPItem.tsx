import styled from '@emotion/styled';
import {Flex} from 'antd';
import {useHover} from 'huse';
import {useParams} from 'react-router-dom';
import {ReactNode} from 'react';
import {MCPCreateLink, MCPCreateNoSpaceLink} from '@/links/mcp';
import {IconRightArrow} from '@/icons/mcp';

interface CreateMCPType {
    name: string;
    description: string;
    type: string;
    icon: ReactNode;
}

const Container = styled(Flex)`
    cursor: pointer;
    width: 360px;
    padding: 12px;
    border-radius: 4px;
`;

const Content = styled.div`
    width: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
`;

const NameText = styled.div`
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
`;

const DescriptionText = styled.div`
    font-size: 12px;
    line-height: 20px;
    color: #5c5c5c;
`;

const IconWrapper = styled.div`
    font-size: 36px;
`;
const CreateMCPItem = ({name, description, type, icon}: CreateMCPType) => {
    const [isHover, hoverCallbacks] = useHover();
    const {workspaceId} = useParams();

    const link = MCPCreateLink.toUrl({
        type, workspaceId: workspaceId ? +workspaceId : undefined,
    });
    const noSpaceCreateLink = MCPCreateNoSpaceLink.toUrl({type});
    // const navigate = useNavigate();
    const handleClick = () => {
        if (workspaceId) {
            // 使用浏览器自身方法打开新页面
            window.open(link, '_blank');
        } else {
            window.open(noSpaceCreateLink, '_blank');
        }
    };

    return (
        <Container
            {...hoverCallbacks}
            align="center"
            gap={8}
            style={isHover && {backgroundColor: '#E5F2FF80'}}
        >
            <IconWrapper>
                {icon}
            </IconWrapper>
            <Flex onClick={handleClick} vertical gap={4} style={{flex: 1}}>
                <Flex justify="space-between" align="center">
                    <NameText>{name}</NameText>
                    <Content>{isHover && <IconRightArrow />}</Content>
                </Flex>
                <DescriptionText>{description}</DescriptionText>
            </Flex>
        </Container>
    );
};

export default CreateMCPItem;
