import {Button} from '@panda-design/components';
import {Popover} from 'antd';
import {PlusOutlined} from '@ant-design/icons';
import {cx} from '@emotion/css';
import {buttonString} from '@/styles/components';
import CreateMCPContent from './CreateMCPContent';

interface Props{
    text?: string;
    className?: string;
}
export const CreateMCPButton = ({text, className}: Props) => {
    return (
        <Popover
            placement="bottomLeft"
            content={<CreateMCPContent />}
            trigger="click"
        >
            <Button icon={<PlusOutlined />} className={cx(buttonString, className)}>
                {text || '创建MCP Server'}
            </Button>
        </Popover>
    );
};
