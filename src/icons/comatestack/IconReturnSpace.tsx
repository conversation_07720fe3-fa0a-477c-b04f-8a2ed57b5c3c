import type { SVGProps } from "react";
const SvgIconReturnSpace = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 20 20"
        {...props}
    >
        <rect
            width={19}
            height={19}
            x={0.5}
            y={0.5}
            stroke="currentColor"
            rx={3.5}
        />
        <path
            fill="currentColor"
            d="M7.219 9.333h8.114v1.334H7.22l3.576 3.576-.943.942L4.667 10l5.185-5.186.943.943z"
        />
    </svg>
);
export default SvgIconReturnSpace;
