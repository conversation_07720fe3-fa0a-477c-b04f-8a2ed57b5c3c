<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.5 6.09961C22.3447 6.09961 27.9004 11.8718 27.9004 19C27.9004 26.1282 22.3447 31.9004 15.5 31.9004C8.65533 31.9004 3.09961 26.1282 3.09961 19C3.09961 11.8718 8.65533 6.09961 15.5 6.09961Z" fill="url(#paint0_linear_1437_10036)" stroke="url(#paint1_linear_1437_10036)" stroke-width="0.2"/>
<foreignObject x="2" y="6.79199" width="52" height="38.415"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1px);clip-path:url(#bgblur_0_1437_10036_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_ii_1437_10036)" data-figma-bg-blur-radius="2">
<path d="M34.4478 8.79199C40.1849 8.79199 44.8358 13.4465 44.8358 19.1882C44.8358 20.4366 44.6155 21.6334 44.2125 22.7423C48.6912 23.8976 52 27.9678 52 32.8109C52 37.1439 49.3506 40.8565 45.5851 42.418C44.7345 42.9183 43.7446 43.2071 42.6866 43.2071H15.4627V43.1826C15.2259 43.1982 14.987 43.2071 14.7463 43.2071C8.81127 43.2071 4 38.392 4 32.4524C4 27.7687 6.99174 23.7845 11.167 22.3089C11.165 22.2249 11.1642 22.1406 11.1642 22.0561C11.1642 16.3145 15.8151 11.6599 21.5522 11.6599C23.2475 11.6599 24.8478 12.0664 26.2614 12.7872C28.1628 10.3551 31.1229 8.79199 34.4478 8.79199Z" fill="#C9E4FF" fill-opacity="0.6"/>
<path d="M34.4482 8.94238C40.1022 8.94264 44.6855 13.5297 44.6855 19.1885C44.6855 20.419 44.4685 21.5987 44.0713 22.6914L44.0146 22.8467L44.1748 22.8877C48.5888 24.0262 51.8495 28.0376 51.8496 32.8105C51.8496 37.0809 49.2388 40.7402 45.5273 42.2793L45.5176 42.2832L45.5088 42.2891C44.6805 42.7762 43.7167 43.0566 42.6865 43.0566H15.6123V43.0225L15.4531 43.0332C15.2192 43.0486 14.9832 43.0566 14.7461 43.0566C8.89413 43.0565 4.15039 38.309 4.15039 32.4521C4.15048 27.8341 7.09979 23.9053 11.2168 22.4502L11.3193 22.4141L11.3174 22.3057C11.3154 22.2231 11.3145 22.1392 11.3145 22.0557C11.3147 16.3971 15.8979 11.8098 21.5518 11.8096C23.2228 11.8096 24.8004 12.2106 26.1934 12.9209L26.3037 12.9766L26.3799 12.8799C28.2541 10.4826 31.1716 8.94238 34.4482 8.94238Z" stroke="url(#paint2_linear_1437_10036)" stroke-width="0.3"/>
</g>
<path d="M29.9995 37.0399C30.6394 37.7077 29.59 38.7327 28.8673 38.088C28.1336 37.4299 27.3975 36.773 26.6626 36.1148C26.3543 35.8394 26.3628 35.3019 26.6553 35.0228C27.3804 34.3257 28.1044 33.6286 28.8295 32.9327C29.5267 32.266 30.5748 33.2605 29.9886 33.9516C33.6034 33.5323 34.9038 29.5653 32.9404 26.7158C31.8045 25.0754 34.4273 23.6092 35.5509 25.2363C38.972 30.1917 35.7459 36.4098 29.9995 37.0399ZM21.6852 32.6341C18.263 27.6799 21.4878 21.4667 27.2305 20.8341C26.5919 20.1675 27.6413 19.1413 28.3628 19.7872C29.0977 20.4429 29.8313 21.0998 30.5687 21.7592C30.8746 22.0322 30.8685 22.5696 30.576 22.8512C29.8508 23.5471 29.1281 24.2418 28.4018 24.9377C27.7071 25.6055 26.6577 24.6098 27.2439 23.9212C23.6303 24.338 22.3287 28.3063 24.2946 31.1545C25.428 32.7925 22.8077 34.2599 21.6852 32.6341Z" fill="white"/>
<defs>
<filter id="filter0_ii_1437_10036" x="2" y="6.79199" width="52" height="38.415" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="0.5"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1437_10036"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="effect1_innerShadow_1437_10036" result="effect2_innerShadow_1437_10036"/>
</filter>
<clipPath id="bgblur_0_1437_10036_clip_path" transform="translate(-2 -6.79199)"><path d="M34.4478 8.79199C40.1849 8.79199 44.8358 13.4465 44.8358 19.1882C44.8358 20.4366 44.6155 21.6334 44.2125 22.7423C48.6912 23.8976 52 27.9678 52 32.8109C52 37.1439 49.3506 40.8565 45.5851 42.418C44.7345 42.9183 43.7446 43.2071 42.6866 43.2071H15.4627V43.1826C15.2259 43.1982 14.987 43.2071 14.7463 43.2071C8.81127 43.2071 4 38.392 4 32.4524C4 27.7687 6.99174 23.7845 11.167 22.3089C11.165 22.2249 11.1642 22.1406 11.1642 22.0561C11.1642 16.3145 15.8151 11.6599 21.5522 11.6599C23.2475 11.6599 24.8478 12.0664 26.2614 12.7872C28.1628 10.3551 31.1229 8.79199 34.4478 8.79199Z"/>
</clipPath><linearGradient id="paint0_linear_1437_10036" x1="1.64964e-07" y1="8" x2="20.021" y2="22.9719" gradientUnits="userSpaceOnUse">
<stop stop-color="#D1E8FF"/>
<stop offset="1" stop-color="#4A8FFF"/>
</linearGradient>
<linearGradient id="paint1_linear_1437_10036" x1="10.2917" y1="7.04" x2="15.8334" y2="17.4474" gradientUnits="userSpaceOnUse">
<stop stop-color="#4785FD" stop-opacity="0.6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1437_10036" x1="15.6486" y1="10.7166" x2="19.0188" y2="20.8194" gradientUnits="userSpaceOnUse">
<stop stop-color="#BDD6FF" stop-opacity="0.4"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
