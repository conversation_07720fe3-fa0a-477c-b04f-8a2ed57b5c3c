import type { SVGProps } from "react";
const SvgAiTools1 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 56 56"
        {...props}
    >
        <path
            fill="url(#aiTools1_svg__a)"
            stroke="url(#aiTools1_svg__b)"
            strokeWidth={0.2}
            d="M11 4.1h18A3.9 3.9 0 0 1 32.9 8v24a3.9 3.9 0 0 1-3.9 3.9H15A7.9 7.9 0 0 1 7.1 28V8A3.9 3.9 0 0 1 11 4.1Z"
        />
        <foreignObject width={37} height={39} x={12} y={9}>
            <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                    backdropFilter: "blur(1px)",
                    clipPath: "url(#aiTools1_svg__c)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <g data-figma-bg-blur-radius={2} filter="url(#aiTools1_svg__d)">
            <path
                fill="#D4EBFF"
                fillOpacity={0.7}
                d="M14 19c0-2.8 0-4.2.545-5.27a5 5 0 0 1 2.185-2.185C17.8 11 19.2 11 22 11h13.943c1.292 0 1.938 0 2.541.153a5 5 0 0 1 1.507.66c.521.34.959.815 1.834 1.765l3.057 3.316c.783.85 1.175 1.274 1.454 1.76.248.432.43.898.54 1.383.124.547.124 1.124.124 2.28V38c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185C43.2 46 41.8 46 39 46H22c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C14 42.2 14 40.8 14 38z"
            />
            <path
                stroke="url(#aiTools1_svg__e)"
                strokeWidth={0.2}
                d="M22 11.1h13.943c1.297 0 1.928.001 2.517.15.524.133 1.024.35 1.477.646.508.332.936.796 1.815 1.75l3.057 3.316c.785.852 1.168 1.268 1.44 1.742.243.423.421.88.53 1.355.12.533.121 1.098.121 2.257V38c0 1.401 0 2.447-.068 3.275s-.202 1.431-.466 1.95a4.9 4.9 0 0 1-2.141 2.141c-.519.264-1.122.398-1.95.466S40.402 45.9 39 45.9H22c-1.401 0-2.447 0-3.275-.068s-1.431-.202-1.95-.466a4.9 4.9 0 0 1-2.141-2.141c-.264-.519-.398-1.122-.466-1.95S14.1 39.402 14.1 38V19c0-1.401 0-2.447.068-3.275s.202-1.431.466-1.95a4.9 4.9 0 0 1 2.141-2.141c.519-.264 1.122-.398 1.95-.466S20.599 11.1 22 11.1Z"
            />
        </g>
        <path
            fill="#fff"
            d="M27.982 34.246c0 1.381-1.103 2.5-2.464 2.5l-.027-.003q-.015.002-.027.003c-1.361 0-2.464-1.119-2.464-2.5 0-1.38 1.103-2.499 2.464-2.499l.031.002.023-.002c.193 0 .378.032.557.076l2.974-4.568a2.5 2.5 0 0 1-.711-1.755c0-1.381 1.103-2.5 2.464-2.5q.014.002.027.003.014-.002.027-.003c1.36 0 2.464 1.119 2.464 2.5 0 .737-.32 1.392-.82 1.85l3.19 4.402.04-.004.027.003.027-.003c1.36 0 2.464 1.119 2.464 2.499s-1.103 2.5-2.464 2.5q-.015-.002-.027-.003-.016.002-.027.003c-1.361 0-2.465-1.12-2.465-2.5 0-.894.467-1.673 1.163-2.114l-3.042-4.201c-.172.04-.346.067-.53.067l-.023-.002-.03.002a2.3 2.3 0 0 1-.64-.1l-2.954 4.537c.473.455.773 1.095.773 1.81"
        />
        <defs>
            <linearGradient
                id="aiTools1_svg__a"
                x1={8.5}
                x2={36.326}
                y1={31.5}
                y2={7.926}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4A8FFF" />
                <stop offset={1} stopColor="#D1E8FF" />
            </linearGradient>
            <linearGradient
                id="aiTools1_svg__b"
                x1={14.583}
                x2={21.999}
                y1={5.28}
                y2={17.048}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4785FD" />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="aiTools1_svg__e"
                x1={42.395}
                x2={33.129}
                y1={11.454}
                y2={18.062}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#C2D7F4" stopOpacity={0.5} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <clipPath id="aiTools1_svg__c" transform="translate(-12 -9)">
                <path d="M14 19c0-2.8 0-4.2.545-5.27a5 5 0 0 1 2.185-2.185C17.8 11 19.2 11 22 11h13.943c1.292 0 1.938 0 2.541.153a5 5 0 0 1 1.507.66c.521.34.959.815 1.834 1.765l3.057 3.316c.783.85 1.175 1.274 1.454 1.76.248.432.43.898.54 1.383.124.547.124 1.124.124 2.28V38c0 2.8 0 4.2-.545 5.27a5 5 0 0 1-2.185 2.185C43.2 46 41.8 46 39 46H22c-2.8 0-4.2 0-5.27-.545a5 5 0 0 1-2.185-2.185C14 42.2 14 40.8 14 38z" />
            </clipPath>
            <filter
                id="aiTools1_svg__d"
                width={37}
                height={39}
                x={12}
                y={9}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feBlend
                    in="SourceGraphic"
                    in2="BackgroundImageFix"
                    result="shape"
                />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={1} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 0.803922 0 0 0 0 0.898039 0 0 0 0 0.984314 0 0 0 1 0" />
                <feBlend in2="shape" result="effect1_innerShadow_1460_517" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dx={-1} />
                <feGaussianBlur stdDeviation={1} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0" />
                <feBlend
                    in2="effect1_innerShadow_1460_517"
                    result="effect2_innerShadow_1460_517"
                />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={0.5} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
                <feBlend
                    in2="effect2_innerShadow_1460_517"
                    result="effect3_innerShadow_1460_517"
                />
            </filter>
        </defs>
    </svg>
);
export default SvgAiTools1;
