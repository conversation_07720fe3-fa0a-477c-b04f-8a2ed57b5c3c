<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="21.1" y="4.1" width="29.8" height="29.8" rx="3.9" fill="url(#paint0_linear_1460_499)" stroke="url(#paint1_linear_1460_499)" stroke-width="0.2"/>
<foreignObject x="3" y="8" width="40" height="40"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(1px);clip-path:url(#bgblur_0_1460_499_clip_path);height:100%;width:100%"></div></foreignObject><g filter="url(#filter0_i_1460_499)" data-figma-bg-blur-radius="2">
<rect x="5" y="10" width="36" height="36" rx="5" fill="#DDF0FF" fill-opacity="0.6"/>
<rect x="5.15" y="10.15" width="35.7" height="35.7" rx="4.85" stroke="url(#paint2_linear_1460_499)" stroke-width="0.3"/>
</g>
<path d="M18 19V22H14V24H18V27H21V19H18ZM23 24H32V22H23V24ZM28 29V32H32V34H28V37H25V29H28ZM23 34H14V32H23V34Z" fill="white"/>
<defs>
<filter id="filter0_i_1460_499" x="3" y="8" width="40" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="1"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.803922 0 0 0 0 0.898039 0 0 0 0 0.984314 0 0 0 1 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1460_499"/>
</filter>
<clipPath id="bgblur_0_1460_499_clip_path" transform="translate(-3 -8)"><rect x="5" y="10" width="36" height="36" rx="5"/>
</clipPath><linearGradient id="paint0_linear_1460_499" x1="24.3333" y1="26" x2="54.9834" y2="8.54648" gradientUnits="userSpaceOnUse">
<stop stop-color="#4A8FFF"/>
<stop offset="1" stop-color="#D1E8FF"/>
</linearGradient>
<linearGradient id="paint1_linear_1460_499" x1="29.75" y1="5.2" x2="36.0026" y2="17.412" gradientUnits="userSpaceOnUse">
<stop stop-color="#4785FD"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_1460_499" x1="23" y1="10" x2="23" y2="46" gradientUnits="userSpaceOnUse">
<stop stop-color="#BDD6FF"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>
