import type { SVGProps } from "react";
const SvgAiTools4 = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 56 56"
        {...props}
    >
        <path
            fill="url(#aiTools4_svg__a)"
            stroke="url(#aiTools4_svg__b)"
            strokeWidth={0.2}
            d="M15.5 6.1c6.845 0 12.4 5.772 12.4 12.9s-5.555 12.9-12.4 12.9S3.1 26.128 3.1 19 8.655 6.1 15.5 6.1Z"
        />
        <foreignObject width={52} height={38.415} x={2} y={6.792}>
            <div
                xmlns="http://www.w3.org/1999/xhtml"
                style={{
                    backdropFilter: "blur(1px)",
                    clipPath: "url(#aiTools4_svg__c)",
                    height: "100%",
                    width: "100%",
                }}
            />
        </foreignObject>
        <g data-figma-bg-blur-radius={2} filter="url(#aiTools4_svg__d)">
            <path
                fill="#C9E4FF"
                fillOpacity={0.6}
                d="M34.448 8.792c5.737 0 10.388 4.655 10.388 10.396 0 1.249-.22 2.445-.623 3.554A10.4 10.4 0 0 1 52 32.811c0 4.333-2.65 8.045-6.415 9.607-.85.5-1.84.79-2.898.79H15.463v-.025q-.355.023-.717.024C8.811 43.207 4 38.392 4 32.452c0-4.683 2.992-8.667 7.167-10.143l-.003-.253c0-5.742 4.651-10.396 10.388-10.396 1.695 0 3.296.406 4.71 1.127a10.37 10.37 0 0 1 8.186-3.995"
            />
            <path
                stroke="url(#aiTools4_svg__e)"
                strokeWidth={0.3}
                d="M34.448 8.942c5.654 0 10.237 4.588 10.237 10.247 0 1.23-.217 2.41-.614 3.502l-.056.156.16.04a10.25 10.25 0 0 1 7.675 9.923c0 4.27-2.611 7.93-6.323 9.47l-.01.003-.008.006a5.55 5.55 0 0 1-2.822.768H15.612v-.035l-.159.011q-.351.024-.707.024c-5.852 0-10.596-4.748-10.596-10.605 0-4.618 2.95-8.547 7.067-10.002l.102-.036-.002-.108-.002-.25c0-5.659 4.583-10.246 10.237-10.246 1.67 0 3.248.4 4.641 1.11l.11.057.077-.097a10.22 10.22 0 0 1 8.068-3.938Z"
            />
        </g>
        <path
            fill="#fff"
            d="M30 37.04c.64.668-.41 1.693-1.133 1.048q-1.101-.986-2.204-1.973c-.309-.276-.3-.813-.008-1.092l2.174-2.09c.698-.667 1.746.328 1.16 1.019 3.614-.42 4.915-4.387 2.951-7.236-1.135-1.64 1.487-3.107 2.61-1.48 3.422 4.956.196 11.174-5.55 11.804m-8.315-4.406c-3.422-4.954-.197-11.167 5.546-11.8-.64-.666.41-1.693 1.132-1.047l2.206 1.972c.306.273.3.81.007 1.092l-2.174 2.087c-.695.668-1.744-.328-1.158-1.017-3.614.417-4.915 4.385-2.95 7.233 1.134 1.638-1.486 3.106-2.609 1.48"
        />
        <defs>
            <linearGradient
                id="aiTools4_svg__a"
                x1={0}
                x2={20.021}
                y1={8}
                y2={22.972}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#D1E8FF" />
                <stop offset={1} stopColor="#4A8FFF" />
            </linearGradient>
            <linearGradient
                id="aiTools4_svg__b"
                x1={10.292}
                x2={15.833}
                y1={7.04}
                y2={17.447}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#4785FD" stopOpacity={0.6} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <linearGradient
                id="aiTools4_svg__e"
                x1={15.649}
                x2={19.019}
                y1={10.717}
                y2={20.819}
                gradientUnits="userSpaceOnUse"
            >
                <stop stopColor="#BDD6FF" stopOpacity={0.4} />
                <stop offset={1} stopColor="#fff" stopOpacity={0} />
            </linearGradient>
            <clipPath id="aiTools4_svg__c" transform="translate(-2 -6.792)">
                <path d="M34.448 8.792c5.737 0 10.388 4.655 10.388 10.396 0 1.249-.22 2.445-.623 3.554A10.4 10.4 0 0 1 52 32.811c0 4.333-2.65 8.045-6.415 9.607-.85.5-1.84.79-2.898.79H15.463v-.025q-.355.023-.717.024C8.811 43.207 4 38.392 4 32.452c0-4.683 2.992-8.667 7.167-10.143l-.003-.253c0-5.742 4.651-10.396 10.388-10.396 1.695 0 3.296.406 4.71 1.127a10.37 10.37 0 0 1 8.186-3.995" />
            </clipPath>
            <filter
                id="aiTools4_svg__d"
                width={52}
                height={38.415}
                x={2}
                y={6.792}
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
            >
                <feFlood floodOpacity={0} result="BackgroundImageFix" />
                <feBlend
                    in="SourceGraphic"
                    in2="BackgroundImageFix"
                    result="shape"
                />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation={0.5} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0" />
                <feBlend in2="shape" result="effect1_innerShadow_1437_10036" />
                <feColorMatrix
                    in="SourceAlpha"
                    result="hardAlpha"
                    values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset dy={2} />
                <feGaussianBlur stdDeviation={1} />
                <feComposite
                    in2="hardAlpha"
                    k2={-1}
                    k3={1}
                    operator="arithmetic"
                />
                <feColorMatrix values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0" />
                <feBlend
                    in2="effect1_innerShadow_1437_10036"
                    result="effect2_innerShadow_1437_10036"
                />
            </filter>
        </defs>
    </svg>
);
export default SvgAiTools4;
