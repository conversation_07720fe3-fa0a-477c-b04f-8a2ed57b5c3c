import type { SVGProps } from "react";
const SvgStar = (props: SVGProps<SVGSVGElement>) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width="1em"
        height="1em"
        fill="none"
        viewBox="0 0 16 16"
        {...props}
    >
        <g clipPath="url(#star_svg__a)">
            <path
                fill="currentColor"
                d="M7.076 11.864a.63.63 0 0 1-1.162 0l-.585-1.34a5.16 5.16 0 0 0-2.627-2.661l-1.61-.715c-.513-.228-.513-.972-.001-1.2l1.56-.692a5.17 5.17 0 0 0 2.665-2.75l.593-1.428a.63.63 0 0 1 1.172 0l.593 1.428a5.17 5.17 0 0 0 2.665 2.75l1.56.692c.512.228.512.972 0 1.2l-1.61.715a5.16 5.16 0 0 0-2.628 2.66zm-4.05-5.316c1.524.676 2.763 1.788 3.469 3.318.706-1.53 1.945-2.641 3.468-3.318-1.542-.684-2.785-1.85-3.468-3.408-.684 1.557-1.927 2.724-3.469 3.408m9.908 8.578.165-.377a2.9 2.9 0 0 1 1.48-1.5l.508-.226a.353.353 0 0 0 0-.642l-.479-.212a2.91 2.91 0 0 1-1.502-1.551l-.17-.408a.338.338 0 0 0-.627 0l-.17.408a2.9 2.9 0 0 1-1.502 1.55l-.478.213a.353.353 0 0 0 0 .642l.507.226a2.9 2.9 0 0 1 1.48 1.5l.165.377c.12.277.503.277.623 0m-.684-2.428.375-.373.367.373-.367.362z"
            />
        </g>
        <defs>
            <clipPath id="star_svg__a">
                <path fill="#fff" d="M0 0h16v16H0z" />
            </clipPath>
        </defs>
    </svg>
);
export default SvgStar;
